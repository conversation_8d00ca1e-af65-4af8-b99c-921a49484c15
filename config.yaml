# General Settings
general:
  env_path: '../.env'
  output_dir: 'reports'
  fyers_api_url: 'https://public.fyers.in/sym_details/NSE_FO.csv'

# Trading Symbols
symbols: 
  - 'NIFTY'
  - 'BANKNIFTY'
  #- 'FINNIFTY'
  #- 'RELIANCE'
  #- 'INFY'
  #- 'DIXON'
  #- 'MCX'
  #- 'BSE'

# Options Filter Settings
options_filter:
  min_volume: 10
  max_volume: *********
  min_ltp_price: 2500
  max_ltp_price: 9000

# Timeframe Settings
timeframe:
  interval: 60  # As shown in interval map in minutes, 1D for days
  days_to_fetch: 30  # Days - Increased for better MAE accuracy

ce_pe_pairing:
  enabled: true
  min_price_percent: 0.0
  max_price_percent: 3.0

# Moving Average Exponential Indicator Settings (using ta library)
mae_indicator:
  enabled: true
  length: 9                # Period for the MAE
  source: 'close'          # Options: open, high, low, closes, hl2, hlc3, ohlc4
  offset: 0                # Can be positive or negative
  smoothing_enabled: false # If True then uses SMA/EMA/WMA to smooth the indicator values
  smoothing_line: 'sma'    # Options: 'sma', 'ema', 'wma' (as per ta library)
  smoothing_length: 9      # Only positive value

