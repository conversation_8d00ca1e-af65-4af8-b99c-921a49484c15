#!/usr/bin/env python3
"""
Test script for CE/PE pairing filter functionality.
"""

import logging
from typing import List
from index_scanner import IndexScanner, FilteredSymbol
from config_loader import ConfigLoader

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_symbols() -> List[FilteredSymbol]:
    """Create test symbols for CE/PE pairing test."""
    test_symbols = [
        # NIFTY25JUL - Valid pair (has both CE and PE)
        FilteredSymbol(
            strike=23000.0, expiry_date="2025-JUL", option_type="CE",
            symbol="NSE:NIFTY25JUL23000CE", ltp=150.0, volume=1000,
            open_price=145.0, high=155.0, low=140.0, close=150.0,
            prev_close=148.0, change=2.0, change_percent=1.35
        ),
        FilteredSymbol(
            strike=23500.0, expiry_date="2025-JUL", option_type="PE",
            symbol="NSE:NIFTY25JUL23500PE", ltp=200.0, volume=800,
            open_price=195.0, high=205.0, low=190.0, close=200.0,
            prev_close=198.0, change=2.0, change_percent=1.01
        ),
        
        # NIFTY25AUG - Invalid pair (only CE, no PE)
        FilteredSymbol(
            strike=24000.0, expiry_date="2025-AUG", option_type="CE",
            symbol="NSE:NIFTY25AUG24000CE", ltp=180.0, volume=1200,
            open_price=175.0, high=185.0, low=170.0, close=180.0,
            prev_close=178.0, change=2.0, change_percent=1.12
        ),
        
        # BANKNIFTY25JUL - Valid pair (has both CE and PE)
        FilteredSymbol(
            strike=50000.0, expiry_date="2025-JUL", option_type="CE",
            symbol="NSE:BANKNIFTY25JUL50000CE", ltp=300.0, volume=500,
            open_price=295.0, high=305.0, low=290.0, close=300.0,
            prev_close=298.0, change=2.0, change_percent=0.67
        ),
        FilteredSymbol(
            strike=51000.0, expiry_date="2025-JUL", option_type="PE",
            symbol="NSE:BANKNIFTY25JUL51000PE", ltp=400.0, volume=600,
            open_price=395.0, high=405.0, low=390.0, close=400.0,
            prev_close=398.0, change=2.0, change_percent=0.50
        ),
        
        # BANKNIFTY25SEP - Invalid pair (only PE, no CE)
        FilteredSymbol(
            strike=52000.0, expiry_date="2025-SEP", option_type="PE",
            symbol="NSE:BANKNIFTY25SEP52000PE", ltp=450.0, volume=700,
            open_price=445.0, high=455.0, low=440.0, close=450.0,
            prev_close=448.0, change=2.0, change_percent=0.45
        ),
    ]
    return test_symbols

def test_ce_pe_pairing_enabled():
    """Test CE/PE pairing filter when enabled."""
    logger.info("Testing CE/PE pairing filter (enabled)...")

    # Create a config with CE/PE pairing enabled
    config = ConfigLoader()
    config._config['ce_pe_pairing'] = {
        'enabled': True,
        'min_price_percent': 0.0,
        'max_price_percent': 50.0  # Increased range to allow test pairs
    }

    # Create scanner instance
    scanner = IndexScanner(config)

    # Create test symbols
    test_symbols = create_test_symbols()
    logger.info(f"Input symbols: {len(test_symbols)}")
    for symbol in test_symbols:
        base = scanner.extract_base_symbol(symbol.symbol)
        logger.info(f"  {symbol.symbol} -> Base: {base}, Type: {symbol.option_type}")

    # Apply CE/PE pairing filter
    paired_symbols = scanner.apply_ce_pe_pairing_filter(test_symbols)

    logger.info(f"Output symbols after pairing: {len(paired_symbols)}")
    for symbol in paired_symbols:
        base = scanner.extract_base_symbol(symbol.symbol)
        logger.info(f"  {symbol.symbol} -> Base: {base}, Type: {symbol.option_type}")

    # Expected: Should keep NIFTY25JUL (2 symbols) and BANKNIFTY25JUL (2 symbols) = 4 total
    expected_count = 4
    if len(paired_symbols) == expected_count:
        logger.info("PASS: CE/PE pairing test PASSED")
        return True
    else:
        logger.error(f"FAIL: CE/PE pairing test FAILED - Expected {expected_count}, got {len(paired_symbols)}")
        return False

def test_ce_pe_pairing_disabled():
    """Test CE/PE pairing filter when disabled."""
    logger.info("Testing CE/PE pairing filter (disabled)...")

    # Create a config with CE/PE pairing disabled
    config = ConfigLoader()
    config._config['ce_pe_pairing'] = {
        'enabled': False,
        'min_price_percent': 0.0,
        'max_price_percent': 3.0
    }

    # Create scanner instance
    scanner = IndexScanner(config)

    # Create test symbols
    test_symbols = create_test_symbols()
    logger.info(f"Input symbols: {len(test_symbols)}")

    # Apply CE/PE pairing filter
    paired_symbols = scanner.apply_ce_pe_pairing_filter(test_symbols)

    logger.info(f"Output symbols after pairing: {len(paired_symbols)}")

    # Expected: Should keep all symbols since pairing is disabled
    expected_count = len(test_symbols)
    if len(paired_symbols) == expected_count:
        logger.info("PASS: CE/PE pairing disabled test PASSED")
        return True
    else:
        logger.error(f"FAIL: CE/PE pairing disabled test FAILED - Expected {expected_count}, got {len(paired_symbols)}")
        return False

def test_base_symbol_extraction():
    """Test base symbol extraction functionality."""
    logger.info("Testing base symbol extraction...")
    
    config = ConfigLoader()
    scanner = IndexScanner(config)
    
    test_cases = [
        ("NSE:NIFTY25JUL23000CE", "NIFTY25JUL"),
        ("NSE:NIFTY25AUG24000PE", "NIFTY25AUG"),
        ("NSE:BANKNIFTY25JUL50000CE", "BANKNIFTY25JUL"),
        ("NSE:BANKNIFTY25SEP52000PE", "BANKNIFTY25SEP"),
        ("INVALID_SYMBOL", "UNKNOWN"),
    ]
    
    all_passed = True
    for symbol, expected_base in test_cases:
        actual_base = scanner.extract_base_symbol(symbol)
        if actual_base == expected_base:
            logger.info(f"PASS: {symbol} -> {actual_base}")
        else:
            logger.error(f"FAIL: {symbol} -> Expected: {expected_base}, Got: {actual_base}")
            all_passed = False

    if all_passed:
        logger.info("PASS: Base symbol extraction test PASSED")
        return True
    else:
        logger.error("FAIL: Base symbol extraction test FAILED")
        return False

def test_price_percentage_filtering():
    """Test price percentage filtering logic."""
    logger.info("Testing price percentage filtering...")

    # Create a config with strict price range
    config = ConfigLoader()
    config._config['ce_pe_pairing'] = {
        'enabled': True,
        'min_price_percent': 0.0,
        'max_price_percent': 10.0  # 10% range
    }

    # Create scanner instance
    scanner = IndexScanner(config)

    # Create test symbols with different price ranges
    test_symbols = [
        # NIFTY25JUL - Valid pair within 10% range
        FilteredSymbol(
            strike=23000.0, expiry_date="2025-JUL", option_type="CE",
            symbol="NSE:NIFTY25JUL23000CE", ltp=100.0, volume=1000,
            open_price=95.0, high=105.0, low=90.0, close=100.0,
            prev_close=98.0, change=2.0, change_percent=2.04
        ),
        FilteredSymbol(
            strike=23500.0, expiry_date="2025-JUL", option_type="PE",
            symbol="NSE:NIFTY25JUL23500PE", ltp=105.0, volume=800,  # 5% difference
            open_price=100.0, high=110.0, low=95.0, close=105.0,
            prev_close=103.0, change=2.0, change_percent=1.94
        ),

        # BANKNIFTY25JUL - Invalid pair outside 10% range
        FilteredSymbol(
            strike=50000.0, expiry_date="2025-JUL", option_type="CE",
            symbol="NSE:BANKNIFTY25JUL50000CE", ltp=100.0, volume=500,
            open_price=95.0, high=105.0, low=90.0, close=100.0,
            prev_close=98.0, change=2.0, change_percent=2.04
        ),
        FilteredSymbol(
            strike=51000.0, expiry_date="2025-JUL", option_type="PE",
            symbol="NSE:BANKNIFTY25JUL51000PE", ltp=150.0, volume=600,  # 50% difference
            open_price=145.0, high=155.0, low=140.0, close=150.0,
            prev_close=148.0, change=2.0, change_percent=1.35
        ),
    ]

    # Apply CE/PE pairing filter
    paired_symbols = scanner.apply_ce_pe_pairing_filter(test_symbols)

    # Expected: Only NIFTY25JUL pair should pass (2 symbols)
    expected_count = 2
    if len(paired_symbols) == expected_count:
        logger.info("PASS: Price percentage filtering test PASSED")
        return True
    else:
        logger.error(f"FAIL: Price percentage filtering test FAILED - Expected {expected_count}, got {len(paired_symbols)}")
        return False

def test_same_expiry_month_requirement():
    """Test that CE/PE pairs must have same expiry month."""
    logger.info("Testing same expiry month requirement...")

    # Create a config with CE/PE pairing enabled
    config = ConfigLoader()
    config._config['ce_pe_pairing'] = {
        'enabled': True,
        'min_price_percent': 0.0,
        'max_price_percent': 50.0
    }

    # Create scanner instance
    scanner = IndexScanner(config)

    # Create test symbols with different expiry months
    test_symbols = [
        # NIFTY25JUL CE
        FilteredSymbol(
            strike=23000.0, expiry_date="2025-JUL", option_type="CE",
            symbol="NSE:NIFTY25JUL23000CE", ltp=100.0, volume=1000,
            open_price=95.0, high=105.0, low=90.0, close=100.0,
            prev_close=98.0, change=2.0, change_percent=2.04
        ),
        # NIFTY25AUG PE (different month - should not pair)
        FilteredSymbol(
            strike=23500.0, expiry_date="2025-AUG", option_type="PE",
            symbol="NSE:NIFTY25AUG23500PE", ltp=105.0, volume=800,
            open_price=100.0, high=110.0, low=95.0, close=105.0,
            prev_close=103.0, change=2.0, change_percent=1.94
        ),
    ]

    # Apply CE/PE pairing filter
    paired_symbols = scanner.apply_ce_pe_pairing_filter(test_symbols)

    # Expected: No pairs should be found (0 symbols)
    expected_count = 0
    if len(paired_symbols) == expected_count:
        logger.info("PASS: Same expiry month requirement test PASSED")
        return True
    else:
        logger.error(f"FAIL: Same expiry month requirement test FAILED - Expected {expected_count}, got {len(paired_symbols)}")
        return False

def main():
    """Run all tests."""
    logger.info("Starting CE/PE pairing filter tests...")

    tests = [
        test_base_symbol_extraction,
        test_ce_pe_pairing_enabled,
        test_ce_pe_pairing_disabled,
        test_price_percentage_filtering,
        test_same_expiry_month_requirement,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
            logger.info("-" * 50)
        except Exception as e:
            logger.error(f"Test failed with exception: {e}")
            logger.info("-" * 50)

    logger.info(f"Test Results: {passed}/{total} tests passed")

    if passed == total:
        logger.info("SUCCESS: All tests PASSED!")
        return True
    else:
        logger.error("FAILURE: Some tests FAILED!")
        return False

if __name__ == "__main__":
    main()