import logging
import pandas as pd
import ta

logger = logging.getLogger(__name__)

# --- MAEAnalyzer: Moving Average Exponential using ta library ---
class MAEAnalyzer:
    """Analyzer for Moving Average Exponential (MAE) using ta library and config options."""
    def __init__(self, length=9, source='close', offset=0, smoothing_line='ema', smoothing_length=9):
        self.length = length
        self.source = source
        self.offset = offset
        self.smoothing_line = smoothing_line
        self.smoothing_length = smoothing_length

    def get_source_series(self, ohlc_data):
        """
        Extracts the correct price series from OHLC data based on config (open, high, low, close, hl2, hlc3, ohlc4).
        ohlc_data: List[OHLCData] or Dict with keys open, high, low, close
        Returns: pd.Series
        """
        df = pd.DataFrame([{k: getattr(row, k) for k in ['open', 'high', 'low', 'close']} for row in ohlc_data])
        if self.source == 'open':
            return df['open']
        elif self.source == 'high':
            return df['high']
        elif self.source == 'low':
            return df['low']
        elif self.source == 'hl2':
            return (df['high'] + df['low']) / 2
        elif self.source == 'hlc3':
            return (df['high'] + df['low'] + df['close']) / 3
        elif self.source == 'ohlc4':
            return (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            return df['close']

    def calculate_mae(self, ohlc_data):
        """
        Calculate two MAE values:
        1. Default MAE (no smoothing): EMA of the selected price series.
        2. Smoothed MAE: EMA of the smoothed (SMA/WMA/EMA) price series.
        Returns: Tuple (mae_default, mae_smoothed) as pd.Series
        """
        series = self.get_source_series(ohlc_data)

        # Default MAE: EMA of the raw series
        mae_default = ta.trend.ema_indicator(series, window=self.length, fillna=False)
        if self.offset != 0:
            mae_default = mae_default.shift(self.offset)

        # Smoothed MAE: EMA of the smoothed series
        if self.smoothing_line == 'sma':
            smoothed = ta.trend.sma_indicator(series, window=self.smoothing_length, fillna=False)
        elif self.smoothing_line == 'wma':
            smoothed = ta.trend.wma_indicator(series, window=self.smoothing_length, fillna=False)
        else:
            smoothed = series.ewm(span=self.smoothing_length, adjust=True).mean()

        #mae_smoothed = smoothed
        mae_smoothed = ta.trend.ema_indicator(smoothed, window=self.length, fillna=False)
        if self.offset != 0:
            mae_smoothed = mae_smoothed.shift(self.offset)

        return mae_default, mae_smoothed

    def is_price_passing_through_mae(self, ohlc_data, current_price=None, use_smoothed=True):
        """
        Check if the latest MAE value falls within the high-low range of the last candle.
        Returns: bool
        use_smoothed: if True, use smoothed MAE; else use default MAE
        current_price: optional, not used in current logic
        """
        mae_default, mae_smoothed = self.calculate_mae(ohlc_data)
        mae_series = mae_smoothed if use_smoothed else mae_default
        if mae_series.empty or pd.isna(mae_series.iloc[-1]):
            return False
        latest_mae = round(mae_series.iloc[-1], 2)
        # Get last candle's high and low
        last_candle = ohlc_data[-1]
        high = getattr(last_candle, 'high', None)
        low = getattr(last_candle, 'low', None)
        #logger.info(f"Last candle: {last_candle}")
        if current_price is not None:
            try:
                percent_distance = ((current_price - latest_mae) / latest_mae) * 100
                logger.info(f"High: {high}, Low: {low}, Latest MAE: {latest_mae}, Current price: {current_price}, Distance: {percent_distance:.2f}%")
            except Exception as e:
                logger.warning(f"Could not calculate percent distance: {e}")
        else:
            logger.info(f"High: {high}, Low: {low}, Latest MAE: {latest_mae}")
        if high is None or low is None:
            return False
        return low <= latest_mae <= high


        print(f"Could not read EMA periods config: {e}")
        return (9, 21)