"""
Report generator for creating CSV reports of filtered symbols.
Generates reports with specified columns and formatting.
"""

import csv
import os
import logging
import re
from datetime import datetime
from typing import List, Dict, Any
from pathlib import Path

from index_scanner import FilteredSymbol

logger = logging.getLogger(__name__)

class ReportGenerator:
    """Generator for creating CSV reports of scan results."""
    
    def __init__(self, output_dir: str = "reports", config=None):
        """
        Initialize the report generator.
        
        Args:
            output_dir: Directory to save reports
            config: ConfigLoader object (optional, required for EMA logic)
        """
        self.output_dir = output_dir
        self.ensure_output_directory()
        self.config = config
        
        # Get configured symbols for dynamic pattern matching
        if config and hasattr(config, 'symbols'):
            self.target_symbols = set(config.symbols)
        else:
            # Fallback to default symbols for backward compatibility
            self.target_symbols = {'NIFTY', 'BANKNIFTY'}
        
        # Create dynamic pattern for underlying extraction
        self._create_underlying_pattern()
    
    def _create_underlying_pattern(self) -> None:
        """
        Create dynamic regex pattern for extracting underlying symbols.
        """
        if not self.target_symbols:
            raise ValueError("No target symbols configured")
        
        # Escape special regex characters in symbol names and create alternation
        escaped_symbols = [re.escape(symbol) for symbol in sorted(self.target_symbols)]
        symbols_pattern = '|'.join(escaped_symbols)
        
        # Create pattern to match underlying at the start of symbol
        self.underlying_pattern = rf'^({symbols_pattern})'
        
        logger.debug(f"Created underlying extraction pattern: {self.underlying_pattern}")
        
    def ensure_output_directory(self) -> None:
        """Ensure the output directory exists."""
        try:
            Path(self.output_dir).mkdir(parents=True, exist_ok=True)
            logger.info(f"Output directory ready: {self.output_dir}")
        except Exception as e:
            logger.error(f"Failed to create output directory {self.output_dir}: {e}")
            raise

    def extract_underlying_from_symbol(self, symbol_str: str) -> str:
        """
        Extract underlying symbol from symbol string using configured symbols.

        Args:
            symbol_str: Symbol string like 'NSE:NIFTY25JUL57000CE'

        Returns:
            Underlying symbol (e.g., 'NIFTY', 'BANKNIFTY', 'RELIANCE') or 'UNKNOWN'
        """
        try:
            # Remove NSE: prefix if present
            clean_symbol = symbol_str.replace('NSE:', '')

            # Extract underlying using dynamic pattern
            match = re.match(self.underlying_pattern, clean_symbol)

            if match:
                return match.group(1)
            else:
                return "UNKNOWN"

        except Exception as e:
            logger.debug(f"Error extracting underlying from {symbol_str}: {e}")
            return "UNKNOWN"

    def extract_month_from_symbol(self, symbol_str: str) -> str:
        """
        Extract month from symbol string using configured symbols.

        Args:
            symbol_str: Symbol string like 'NSE:NIFTY25JUL57000CE'

        Returns:
            Month abbreviation like 'JUL'
        """
        try:
            # Remove NSE: prefix if present
            clean_symbol = symbol_str.replace('NSE:', '')

            # Create dynamic pattern for month extraction
            escaped_symbols = [re.escape(symbol) for symbol in sorted(self.target_symbols)]
            symbols_pattern = '|'.join(escaped_symbols)
            pattern = rf'^({symbols_pattern})(\d{{2}})([A-Z]{{3}})'
            
            match = re.match(pattern, clean_symbol)

            if match:
                return match.group(3)
            else:
                return "UNKNOWN"

        except Exception as e:
            logger.debug(f"Error extracting month from {symbol_str}: {e}")
            return "UNKNOWN"

    def sort_symbols_by_underlying_and_month(self, filtered_symbols: List[FilteredSymbol]) -> List[FilteredSymbol]:
        """
        Sort symbols by underlying (NIFTY first, then BANKNIFTY) and then by month sequence.

        Args:
            filtered_symbols: List of FilteredSymbol objects

        Returns:
            Sorted list of FilteredSymbol objects
        """
        # Define month order for sorting
        month_order = {
            'JUL': 1, 'AUG': 2, 'SEP': 3, 'OCT': 4, 'NOV': 5, 'DEC': 6,
            'JAN': 7, 'FEB': 8, 'MAR': 9, 'APR': 10, 'MAY': 11, 'JUN': 12
        }

        # Define underlying order (NIFTY first, then BANKNIFTY)
        underlying_order = {'NIFTY': 1, 'BANKNIFTY': 2, 'FINNIFTY': 3}

        def sort_key(symbol: FilteredSymbol):
            underlying = self.extract_underlying_from_symbol(symbol.symbol)
            month = self.extract_month_from_symbol(symbol.symbol)

            underlying_priority = underlying_order.get(underlying, 999)
            month_priority = month_order.get(month, 999)

            # Sort by: underlying, month, strike price, option type
            return (underlying_priority, month_priority, symbol.strike, symbol.option_type)

        sorted_symbols = sorted(filtered_symbols, key=sort_key)

        logger.info(f"Sorted {len(sorted_symbols)} symbols by underlying and month sequence")
        return sorted_symbols
    
    def generate_filename(self, prefix: str = "index_scan") -> str:
        """
        Generate a timestamped filename for the report.
        
        Args:
            prefix: Prefix for the filename
            
        Returns:
            Generated filename with timestamp
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{prefix}_{timestamp}.csv"
        return os.path.join(self.output_dir, filename)
    
    def create_csv_report(self, filtered_symbols: List[FilteredSymbol],
                         filename: str = None) -> str:
        """
        Create a CSV report with filtered symbols.

        Args:
            filtered_symbols: List of FilteredSymbol objects
            filename: Optional custom filename

        Returns:
            Path to the created CSV file
        """
        if filename is None:
            filename = self.generate_filename()

        # Sort symbols by underlying and month sequence
        sorted_symbols = self.sort_symbols_by_underlying_and_month(filtered_symbols)

        # Conditionally add MAE column only
        mae_enabled = False
        if self.config:
            mae_enabled = getattr(self.config, 'mae_enabled', False)

        headers = [
            'strike',
            'expiry_date',
            'type',
            'symbol',
            'LTP',
            'volume',
            'open',
            'high',
            'low',
            'close',
            'prev_close',
            'change',
            'change_percent',
        ]
        if mae_enabled:
            headers += ['mae_value', 'mae_type']

        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # Write headers
                writer.writerow(headers)

                # Write data rows
                for symbol in sorted_symbols:
                    row = [
                        symbol.strike,
                        symbol.expiry_date,
                        symbol.option_type,
                        symbol.symbol,
                        round(symbol.ltp, 2),
                        symbol.volume,
                        round(symbol.open_price, 2),
                        round(symbol.high, 2),
                        round(symbol.low, 2),
                        round(symbol.close, 2),
                        round(symbol.prev_close, 2),
                        round(symbol.change, 2),
                        round(symbol.change_percent, 2),
                    ]
                    if mae_enabled:
                        # Determine which MAE type was used (default or smoothed)
                        mae_type = 'smoothed' if getattr(self.config, 'mae_smoothing_enabled', False) else 'default'
                        row += [round(symbol.ema_short, 2) if symbol.ema_short is not None else '', mae_type]
                    writer.writerow(row)
            
            logger.info(f"CSV report created successfully: {filename}")
            logger.info(f"Report contains {len(filtered_symbols)} symbols")
            return filename
            
        except Exception as e:
            logger.error(f"Failed to create CSV report: {e}")
            raise
    
    def create_summary_report(self, filtered_symbols: List[FilteredSymbol],
                            summary_stats: Dict[str, Any],
                            filename: str = None) -> str:
        """
        Create a summary report with statistics.
        
        Args:
            filtered_symbols: List of FilteredSymbol objects
            summary_stats: Dictionary with summary statistics
            filename: Optional custom filename
            
        Returns:
            Path to the created summary file
        """
        if filename is None:
            base_filename = self.generate_filename("index_scan_summary")
            filename = base_filename.replace('.csv', '.txt')
        
        try:
            with open(filename, 'w', encoding='utf-8') as file:
                file.write("INDEX SCANNER SUMMARY REPORT\n")
                file.write("=" * 50 + "\n\n")
                
                # Timestamp
                file.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # Summary statistics
                file.write("SUMMARY STATISTICS:\n")
                file.write("-" * 20 + "\n")
                file.write(f"Total Symbols Found: {summary_stats.get('total_symbols', 0)}\n")
                file.write(f"NIFTY Options: {summary_stats.get('nifty_symbols', 0)}\n")
                file.write(f"BANKNIFTY Options: {summary_stats.get('banknifty_symbols', 0)}\n")
                file.write(f"Call Options (CE): {summary_stats.get('ce_options', 0)}\n")
                file.write(f"Put Options (PE): {summary_stats.get('pe_options', 0)}\n\n")
                
                # Top symbols by volume
                if filtered_symbols:
                    file.write("TOP 10 SYMBOLS BY VOLUME:\n")
                    file.write("-" * 30 + "\n")
                    
                    # Sort by volume and take top 10
                    top_by_volume = sorted(filtered_symbols, key=lambda x: x.volume, reverse=True)[:10]
                    
                    for i, symbol in enumerate(top_by_volume, 1):
                        file.write(f"{i:2d}. {symbol.symbol:<30} Volume: {symbol.volume:>10,} LTP: {symbol.ltp:>8.2f}\n")
                    
                    file.write("\n")
                    
                    # Top symbols by LTP
                    file.write("TOP 10 SYMBOLS BY LTP:\n")
                    file.write("-" * 25 + "\n")
                    
                    top_by_ltp = sorted(filtered_symbols, key=lambda x: x.ltp, reverse=True)[:10]
                    
                    for i, symbol in enumerate(top_by_ltp, 1):
                        file.write(f"{i:2d}. {symbol.symbol:<30} LTP: {symbol.ltp:>8.2f} Volume: {symbol.volume:>10,}\n")
            
            logger.info(f"Summary report created successfully: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"Failed to create summary report: {e}")
            raise
    
    def print_console_summary(self, filtered_symbols: List[FilteredSymbol],
                            summary_stats: Dict[str, Any]) -> None:
        """
        Print a summary to console for immediate feedback.
        
        Args:
            filtered_symbols: List of FilteredSymbol objects
            summary_stats: Dictionary with summary statistics
        """
        print("\n" + "=" * 60)
        print("INDEX SCANNER RESULTS SUMMARY")
        print("=" * 60)
        
        print(f"Total Symbols Found: {summary_stats.get('total_symbols', 0)}")
        print(f"NIFTY Options: {summary_stats.get('nifty_symbols', 0)}")
        print(f"BANKNIFTY Options: {summary_stats.get('banknifty_symbols', 0)}")
        print(f"Call Options (CE): {summary_stats.get('ce_options', 0)}")
        print(f"Put Options (PE): {summary_stats.get('pe_options', 0)}")
        
        if filtered_symbols:
            print("\nTOP 5 SYMBOLS BY VOLUME:")
            print("-" * 40)
            
            top_by_volume = sorted(filtered_symbols, key=lambda x: x.volume, reverse=True)[:5]
            
            for i, symbol in enumerate(top_by_volume, 1):
                print(f"{i}. {symbol.symbol} - Volume: {symbol.volume:,}, LTP: {symbol.ltp:.2f}")
        
        print("=" * 60 + "\n")
    
    def generate_full_report(self, filtered_symbols: List[FilteredSymbol],
                           summary_stats: Dict[str, Any]) -> Dict[str, str]:
        """
        Generate both CSV and summary reports.
        
        Args:
            filtered_symbols: List of FilteredSymbol objects
            summary_stats: Dictionary with summary statistics
            
        Returns:
            Dictionary with paths to created files
        """
        try:
            # Generate CSV report
            csv_file = self.create_csv_report(filtered_symbols)
            
            # Generate summary report
            summary_file = self.create_summary_report(filtered_symbols, summary_stats)
            
            # Print console summary
            self.print_console_summary(filtered_symbols, summary_stats)
            
            return {
                'csv_report': csv_file,
                'summary_report': summary_file
            }
            
        except Exception as e:
            logger.error(f"Failed to generate full report: {e}")
            raise
