{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:51,849+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:52,069+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:52,273+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:52,479+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:52,589+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:52,785+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:52,990+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:53,196+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:53,403+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:53,546+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:53,708+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:53,963+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:54,220+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:54,423+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:54,629+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:54,834+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:55,039+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:55,243+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:55,451+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:55,654+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:55,841+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:56,165+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:56,269+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:56,471+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:56,779+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:57,086+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:57,292+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:57,702+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:57,844+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:58,009+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:58,214+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:58,527+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:58,827+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:59,032+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:59,135+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:59,339+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:59,544+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:59,750+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:59,852+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:59:00,056+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:59:00,363+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:59:00,671+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:59:00,838+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:59:00,977+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:10,620+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:10,783+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:11,030+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:11,337+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:11,439+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:11,702+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:11,848+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:12,053+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:12,360+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:12,463+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:12,567+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:12,776+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:12,881+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:13,086+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:13,386+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:13,590+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:13,692+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:13,805+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:14,102+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:14,204+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:14,304+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:14,581+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:14,716+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:14,921+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:15,127+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:15,433+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:15,536+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:15,661+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:15,945+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:16,149+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:16,458+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:16,560+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:16,764+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:16,972+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:17,212+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:17,373+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:17,481+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:17,619+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:17,789+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:17,919+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:18,044+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:18,300+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:18,394+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:18,531+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:13,644+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:13,952+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:14,361+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:14,668+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:14,976+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:15,181+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:15,590+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:15,898+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:16,204+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:16,410+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:12,730+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:12,833+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:13,038+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:13,243+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:13,788+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:14,382+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:15,004+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:15,629+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:16,189+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:16,580+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-02 01:48:22,536+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-02 01:48:22,640+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-02 01:48:22,743+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-02 01:48:23,187+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-02 01:48:23,561+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-02 01:48:23,963+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-02 01:48:24,395+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-02 01:48:24,804+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:27,583+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:27,787+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:27,884+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:28,093+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:28,262+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:28,401+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:28,542+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:28,649+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:28,765+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:28,882+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:29,043+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:29,229+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:29,363+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:29,498+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:29,625+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:29,811+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:29,904+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:30,034+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:30,244+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:30,338+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:30,477+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:30,655+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:30,858+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:31,063+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:31,268+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:31,457+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:31,680+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:31,883+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:32,087+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:32,193+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:32,394+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:32,490+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:32,600+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:32,702+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:32,808+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:32,980+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:33,124+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:33,329+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:33,521+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:33,725+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:33,831+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:34,021+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:34,134+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:34,301+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:34,491+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:34,749+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:34,840+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:35,057+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:35,262+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:35,428+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:35,568+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:35,675+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:35,876+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:36,096+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:36,267+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:36,419+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:36,514+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:36,696+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:36,833+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:36,958+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:37,105+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:37,310+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:37,515+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:37,719+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:37,886+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:38,034+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:38,189+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:38,329+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:38,451+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:38,557+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:38,743+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:38,878+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:39,077+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:39,174+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:39,295+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:39,404+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:39,554+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:39,660+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:39,757+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:39,873+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:39,972+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:40,215+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:40,382+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:40,542+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:40,784+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:40,996+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:41,110+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:41,307+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:41,461+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:41,559+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:41,771+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:41,918+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:42,123+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:42,249+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:42,350+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:42,449+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:42,573+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:42,791+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:42,892+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:43,025+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:43,114+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:43,249+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:43,351+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:43,557+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:43,671+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:43,879+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:44,017+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:44,122+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:44,273+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:44,480+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:44,614+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:44,754+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:44,887+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:45,046+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:45,146+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:45,297+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:45,399+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:45,504+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:45,607+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:45,723+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:45,854+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:45,965+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:46,140+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:46,303+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:46,413+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:46,535+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:46,630+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:46,782+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:46,935+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:47,077+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:47,193+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:47,385+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:47,550+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:47,698+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:47,913+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:48,063+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:48,219+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:48,369+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:48,532+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:48,677+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:48,860+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:48,987+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:49,245+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:49,354+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:49,496+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:49,618+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:49,720+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:49,966+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:50,058+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:50,164+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:50,315+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:21:50,501+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:27,225+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:27,360+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:27,486+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:27,589+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:27,795+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:27,953+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:28,102+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:28,218+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:28,409+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:28,512+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:28,898+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:29,455+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:30,064+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:30,475+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:31,083+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:31,640+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:32,020+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:32,197+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:32,402+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:32,570+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:32,709+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:32,838+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:33,017+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:33,175+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:33,324+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:33,495+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:33,632+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:33,870+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:34,002+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:34,143+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:34,327+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:34,485+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:34,576+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:34,717+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:34,855+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:35,016+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:35,143+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:35,243+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:35,374+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:35,477+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:35,661+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:35,833+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:35,976+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:36,070+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:36,172+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:36,296+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:36,391+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-03 15:22:36,515+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:09,882+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:10,085+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:10,199+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:10,393+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:10,598+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:10,803+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:10,917+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:11,063+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:11,212+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:11,336+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:11,486+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:11,725+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:11,930+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:12,031+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:12,134+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:12,238+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:12,442+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:12,749+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:12,851+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:13,057+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:13,163+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:13,363+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:13,469+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:13,670+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:13,876+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:13,986+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-05 14:59:14,154+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:37,058+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:37,201+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:37,411+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:37,553+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:37,706+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:37,845+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:37,963+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:38,056+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:38,179+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:38,335+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:38,474+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:38,631+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:38,784+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:38,916+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:39,019+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:39,147+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:39,293+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:39,418+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:39,563+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:39,702+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:39,888+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:39,998+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:40,160+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:40,410+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:40,560+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:40,720+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:40,841+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:40,954+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:41,136+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:41,243+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:41,363+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:41,463+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:41,589+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:41,713+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:41,840+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:41,990+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:42,117+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:42,257+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:42,441+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:42,586+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:42,719+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:42,841+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:42,980+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:43,137+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:43,242+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:43,371+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:43,546+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:43,653+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:43,798+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:43,943+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:44,065+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:44,203+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:44,386+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:44,516+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:44,645+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:44,766+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:44,923+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:45,096+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:45,246+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:45,360+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:45,500+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:45,674+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:45,825+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:45,950+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:46,145+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:46,286+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:46,422+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:46,564+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:46,699+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-05 20:09:46,823+0530","service":"FyersAPI"}
