root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-06 00:00:59
fyers_config - INFO - Loading configuration...
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\index_scanner\config.yaml
config_loader - INFO - Configuration validation successful
fyers_config - INFO - Configuration loaded and validated successfully
fyers_config - INFO - Target symbols: ['NIFTY', 'BANKNIFTY']
fyers_config - INFO - Volume filter: >= 10
fyers_config - INFO - LTP filter: 2500 - 9000
fyers_config - INFO - Initializing Index Scanner...
symbol_parser - INFO - Using configured target symbols: BANKNIFTY, NIFTY
fyers_config - INFO - Starting symbol scanning process...
index_scanner - INFO - Starting index scanner...
index_scanner - INFO - Authenticating with Fyers API...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - 
=== Fyers API Authentication ===
fyers_config - INFO - A browser window will open for you to log in to Fyers.
fyers_config - INFO - After logging in, you will be redirected to Google.
fyers_config - INFO - Copy the auth code from the URL and paste it here.
fyers_config - INFO - 
Please login in the private browser window that opened.
fyers_config - INFO - Authentication files saved to C:\Users\<USER>\Desktop\Python\index_scanner
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
index_scanner - INFO - Loading symbols from CSV...
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Loaded 3566 symbols from CSV for target months
symbol_parser - INFO - Filtered to 3566 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Prepared 3566 symbols for scanning
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Loaded 3566 symbols from CSV for target months
symbol_parser - INFO - Filtered to 3566 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Saved 3566 filtered symbols to reports\filtered_symbols.csv
index_scanner - INFO - Found 3566 symbols to scan
index_scanner - INFO - Fetching market data from Fyers API...
fyers_client - INFO - Fetching quotes for batch 1: 50 symbols
fyers_client - INFO - Fetching quotes for batch 2: 50 symbols
fyers_client - INFO - Fetching quotes for batch 3: 50 symbols
fyers_client - INFO - Fetching quotes for batch 4: 50 symbols
fyers_client - INFO - Fetching quotes for batch 5: 50 symbols
fyers_client - INFO - Fetching quotes for batch 6: 50 symbols
fyers_client - INFO - Fetching quotes for batch 7: 50 symbols
fyers_client - INFO - Fetching quotes for batch 8: 50 symbols
fyers_client - INFO - Fetching quotes for batch 9: 50 symbols
fyers_client - INFO - Fetching quotes for batch 10: 50 symbols
fyers_client - INFO - Fetching quotes for batch 11: 50 symbols
fyers_client - INFO - Fetching quotes for batch 12: 50 symbols
fyers_client - INFO - Fetching quotes for batch 13: 50 symbols
fyers_client - INFO - Fetching quotes for batch 14: 50 symbols
fyers_client - INFO - Fetching quotes for batch 15: 50 symbols
fyers_client - INFO - Fetching quotes for batch 16: 50 symbols
fyers_client - INFO - Fetching quotes for batch 17: 50 symbols
fyers_client - INFO - Fetching quotes for batch 18: 50 symbols
fyers_client - INFO - Fetching quotes for batch 19: 50 symbols
fyers_client - INFO - Fetching quotes for batch 20: 50 symbols
fyers_client - INFO - Fetching quotes for batch 21: 50 symbols
fyers_client - INFO - Fetching quotes for batch 22: 50 symbols
fyers_client - INFO - Fetching quotes for batch 23: 50 symbols
fyers_client - INFO - Fetching quotes for batch 24: 50 symbols
fyers_client - INFO - Fetching quotes for batch 25: 50 symbols
fyers_client - INFO - Fetching quotes for batch 26: 50 symbols
fyers_client - INFO - Fetching quotes for batch 27: 50 symbols
fyers_client - INFO - Fetching quotes for batch 28: 50 symbols
fyers_client - INFO - Fetching quotes for batch 29: 50 symbols
fyers_client - INFO - Fetching quotes for batch 30: 50 symbols
fyers_client - INFO - Fetching quotes for batch 31: 50 symbols
fyers_client - INFO - Fetching quotes for batch 32: 50 symbols
fyers_client - INFO - Fetching quotes for batch 33: 50 symbols
fyers_client - INFO - Fetching quotes for batch 34: 50 symbols
fyers_client - INFO - Fetching quotes for batch 35: 50 symbols
fyers_client - INFO - Fetching quotes for batch 36: 50 symbols
fyers_client - INFO - Fetching quotes for batch 37: 50 symbols
fyers_client - INFO - Fetching quotes for batch 38: 50 symbols
fyers_client - INFO - Fetching quotes for batch 39: 50 symbols
fyers_client - INFO - Fetching quotes for batch 40: 50 symbols
fyers_client - INFO - Fetching quotes for batch 41: 50 symbols
fyers_client - INFO - Fetching quotes for batch 42: 50 symbols
fyers_client - INFO - Fetching quotes for batch 43: 50 symbols
fyers_client - INFO - Fetching quotes for batch 44: 50 symbols
fyers_client - INFO - Fetching quotes for batch 45: 50 symbols
fyers_client - INFO - Fetching quotes for batch 46: 50 symbols
fyers_client - INFO - Fetching quotes for batch 47: 50 symbols
fyers_client - INFO - Fetching quotes for batch 48: 50 symbols
fyers_client - INFO - Fetching quotes for batch 49: 50 symbols
fyers_client - INFO - Fetching quotes for batch 50: 50 symbols
fyers_client - INFO - Fetching quotes for batch 51: 50 symbols
fyers_client - INFO - Fetching quotes for batch 52: 50 symbols
fyers_client - INFO - Fetching quotes for batch 53: 50 symbols
fyers_client - INFO - Fetching quotes for batch 54: 50 symbols
fyers_client - INFO - Fetching quotes for batch 55: 50 symbols
fyers_client - INFO - Fetching quotes for batch 56: 50 symbols
fyers_client - INFO - Fetching quotes for batch 57: 50 symbols
fyers_client - INFO - Fetching quotes for batch 58: 50 symbols
fyers_client - INFO - Fetching quotes for batch 59: 50 symbols
fyers_client - INFO - Fetching quotes for batch 60: 50 symbols
fyers_client - INFO - Fetching quotes for batch 61: 50 symbols
fyers_client - INFO - Fetching quotes for batch 62: 50 symbols
fyers_client - INFO - Fetching quotes for batch 63: 50 symbols
fyers_client - INFO - Fetching quotes for batch 64: 50 symbols
fyers_client - INFO - Fetching quotes for batch 65: 50 symbols
fyers_client - INFO - Fetching quotes for batch 66: 50 symbols
fyers_client - INFO - Fetching quotes for batch 67: 50 symbols
fyers_client - INFO - Fetching quotes for batch 68: 50 symbols
fyers_client - INFO - Fetching quotes for batch 69: 50 symbols
fyers_client - INFO - Fetching quotes for batch 70: 50 symbols
fyers_client - INFO - Fetching quotes for batch 71: 50 symbols
fyers_client - INFO - Fetching quotes for batch 72: 16 symbols
fyers_client - INFO - Successfully fetched market data for 3566 symbols
index_scanner - INFO - Received market data for 3566 symbols
index_scanner - INFO - Applying filters...
index_scanner - INFO - Applying filters - Volume Range: 10-*********, LTP Range: 2500-9000
index_scanner - INFO - CE/PE Pairing: Enabled
index_scanner - INFO - MAE Analysis: Enabled
index_scanner - INFO - MAE Length: 9, Source: close, Offset: 0, Smoothing: sma(9)
index_scanner - INFO - Timeframe - Interval: 60, Days: 30
index_scanner - INFO - First symbol for BANKNIFTY JUL: NSE:BANKNIFTY25JUL49000CE (LTP: 8211.0, Volume: 210)
index_scanner - INFO - First symbol for NIFTY JUL: NSE:NIFTY25JUL22500CE (LTP: 3020.0, Volume: 12150)
index_scanner - INFO - First symbol for BANKNIFTY AUG: NSE:BANKNIFTY25AUG52000CE (LTP: 5490.0, Volume: 315)
index_scanner - INFO - First symbol for NIFTY AUG: NSE:NIFTY25AUG23000CE (LTP: 2532.0, Volume: 225)
index_scanner - INFO - First symbol for BANKNIFTY SEP: NSE:BANKNIFTY25SEP55000CE (LTP: 2999.0, Volume: 455)
index_scanner - INFO - First symbol for NIFTY SEP: NSE:NIFTY25SEP17000CE (LTP: 8585.0, Volume: 450)
index_scanner - INFO - Initial Filtering Results:
index_scanner - INFO -   Total symbols processed: 3566
index_scanner - INFO -   Passed volume filter: 700
index_scanner - INFO -   Passed LTP filter: 42
index_scanner - INFO - Applying CE/PE pairing filter with price range: 0.0% - 3.0%
index_scanner - INFO - CE/PE Pairing Results:
index_scanner - INFO -   Total base groups processed: 6
index_scanner - INFO -   Valid paired groups: 2
index_scanner - INFO -   Discarded groups (missing CE/PE): 3
index_scanner - INFO -   Price filtered groups: 1
index_scanner - INFO -   Symbols before pairing: 42
index_scanner - INFO -   Symbols after pairing: 8
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL51500CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 82 OHLC data points for NSE:BANKNIFTY25JUL51500CE
technical_indicators - INFO - High: 5808.55, Low: 5799.45, Latest MAE: 5697.07, Current price: 5799.45, Distance: 1.80%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL51700CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 7 OHLC data points for NSE:BANKNIFTY25JUL51700CE
index_scanner - WARNING - Insufficient historical data for MAE calculation: NSE:BANKNIFTY25JUL51700CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54500CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 147 OHLC data points for NSE:BANKNIFTY25JUL54500CE
technical_indicators - INFO - High: 2880.0, Low: 2880.0, Latest MAE: 2780.81, Current price: 2880.0, Distance: 3.57%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL63000PE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 79 OHLC data points for NSE:BANKNIFTY25JUL63000PE
technical_indicators - INFO - High: 5700.0, Low: 5640.55, Latest MAE: 5755.77, Current price: 5640.55, Distance: -2.00%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL59900PE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 16 OHLC data points for NSE:BANKNIFTY25JUL59900PE
technical_indicators - INFO - High: 2799.85, Low: 2799.85, Latest MAE: 2946.33, Current price: 2799.85, Distance: -4.97%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG54900CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 7 OHLC data points for NSE:BANKNIFTY25AUG54900CE
index_scanner - WARNING - Insufficient historical data for MAE calculation: NSE:BANKNIFTY25AUG54900CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG55000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 145 OHLC data points for NSE:BANKNIFTY25AUG55000CE
technical_indicators - INFO - High: 2799.95, Low: 2755.0, Latest MAE: 2692.78, Current price: 2755.0, Distance: 2.31%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG60000PE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 53 OHLC data points for NSE:BANKNIFTY25AUG60000PE
technical_indicators - INFO - High: 2726.0, Low: 2726.0, Latest MAE: 2816.19, Current price: 2726.0, Distance: -3.20%
index_scanner - INFO - Final Filtering Results:
index_scanner - INFO -   Symbols after CE/PE pairing: 8
index_scanner - INFO -   Passed MAE filter: 0
index_scanner - INFO -   Final symbols after all filters: 0
index_scanner - INFO -   Overall success rate: 0.00%
index_scanner - INFO - Scanning complete. Found 0 symbols matching criteria
fyers_config - WARNING - No symbols found matching the criteria
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-06 00:03:12
fyers_config - INFO - Loading configuration...
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\index_scanner\config.yaml
config_loader - INFO - Configuration validation successful
fyers_config - INFO - Configuration loaded and validated successfully
fyers_config - INFO - Target symbols: ['NIFTY', 'BANKNIFTY']
fyers_config - INFO - Volume filter: >= 10
fyers_config - INFO - LTP filter: 2500 - 9000
fyers_config - INFO - Initializing Index Scanner...
symbol_parser - INFO - Using configured target symbols: BANKNIFTY, NIFTY
fyers_config - INFO - Starting symbol scanning process...
index_scanner - INFO - Starting index scanner...
index_scanner - INFO - Authenticating with Fyers API...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
index_scanner - INFO - Loading symbols from CSV...
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Loaded 3566 symbols from CSV for target months
symbol_parser - INFO - Filtered to 3566 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Prepared 3566 symbols for scanning
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Loaded 3566 symbols from CSV for target months
symbol_parser - INFO - Filtered to 3566 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Saved 3566 filtered symbols to reports\filtered_symbols.csv
index_scanner - INFO - Found 3566 symbols to scan
index_scanner - INFO - Fetching market data from Fyers API...
fyers_client - INFO - Fetching quotes for batch 1: 50 symbols
fyers_client - INFO - Fetching quotes for batch 2: 50 symbols
fyers_client - INFO - Fetching quotes for batch 3: 50 symbols
fyers_client - INFO - Fetching quotes for batch 4: 50 symbols
fyers_client - INFO - Fetching quotes for batch 5: 50 symbols
fyers_client - INFO - Fetching quotes for batch 6: 50 symbols
fyers_client - INFO - Fetching quotes for batch 7: 50 symbols
fyers_client - INFO - Fetching quotes for batch 8: 50 symbols
fyers_client - INFO - Fetching quotes for batch 9: 50 symbols
fyers_client - INFO - Fetching quotes for batch 10: 50 symbols
fyers_client - INFO - Fetching quotes for batch 11: 50 symbols
fyers_client - INFO - Fetching quotes for batch 12: 50 symbols
fyers_client - INFO - Fetching quotes for batch 13: 50 symbols
fyers_client - INFO - Fetching quotes for batch 14: 50 symbols
fyers_client - INFO - Fetching quotes for batch 15: 50 symbols
fyers_client - INFO - Fetching quotes for batch 16: 50 symbols
fyers_client - INFO - Fetching quotes for batch 17: 50 symbols
fyers_client - INFO - Fetching quotes for batch 18: 50 symbols
fyers_client - INFO - Fetching quotes for batch 19: 50 symbols
fyers_client - INFO - Fetching quotes for batch 20: 50 symbols
fyers_client - INFO - Fetching quotes for batch 21: 50 symbols
fyers_client - INFO - Fetching quotes for batch 22: 50 symbols
fyers_client - INFO - Fetching quotes for batch 23: 50 symbols
fyers_client - INFO - Fetching quotes for batch 24: 50 symbols
fyers_client - INFO - Fetching quotes for batch 25: 50 symbols
fyers_client - INFO - Fetching quotes for batch 26: 50 symbols
fyers_client - INFO - Fetching quotes for batch 27: 50 symbols
fyers_client - INFO - Fetching quotes for batch 28: 50 symbols
fyers_client - INFO - Fetching quotes for batch 29: 50 symbols
fyers_client - INFO - Fetching quotes for batch 30: 50 symbols
fyers_client - INFO - Fetching quotes for batch 31: 50 symbols
fyers_client - INFO - Fetching quotes for batch 32: 50 symbols
fyers_client - INFO - Fetching quotes for batch 33: 50 symbols
fyers_client - INFO - Fetching quotes for batch 34: 50 symbols
fyers_client - INFO - Fetching quotes for batch 35: 50 symbols
fyers_client - INFO - Fetching quotes for batch 36: 50 symbols
fyers_client - INFO - Fetching quotes for batch 37: 50 symbols
fyers_client - INFO - Fetching quotes for batch 38: 50 symbols
fyers_client - INFO - Fetching quotes for batch 39: 50 symbols
fyers_client - INFO - Fetching quotes for batch 40: 50 symbols
fyers_client - INFO - Fetching quotes for batch 41: 50 symbols
fyers_client - INFO - Fetching quotes for batch 42: 50 symbols
fyers_client - INFO - Fetching quotes for batch 43: 50 symbols
fyers_client - INFO - Fetching quotes for batch 44: 50 symbols
fyers_client - INFO - Fetching quotes for batch 45: 50 symbols
fyers_client - INFO - Fetching quotes for batch 46: 50 symbols
fyers_client - INFO - Fetching quotes for batch 47: 50 symbols
fyers_client - INFO - Fetching quotes for batch 48: 50 symbols
fyers_client - INFO - Fetching quotes for batch 49: 50 symbols
fyers_client - INFO - Fetching quotes for batch 50: 50 symbols
fyers_client - INFO - Fetching quotes for batch 51: 50 symbols
fyers_client - INFO - Fetching quotes for batch 52: 50 symbols
fyers_client - INFO - Fetching quotes for batch 53: 50 symbols
fyers_client - INFO - Fetching quotes for batch 54: 50 symbols
fyers_client - INFO - Fetching quotes for batch 55: 50 symbols
fyers_client - INFO - Fetching quotes for batch 56: 50 symbols
fyers_client - INFO - Fetching quotes for batch 57: 50 symbols
fyers_client - INFO - Fetching quotes for batch 58: 50 symbols
fyers_client - INFO - Fetching quotes for batch 59: 50 symbols
fyers_client - INFO - Fetching quotes for batch 60: 50 symbols
fyers_client - INFO - Fetching quotes for batch 61: 50 symbols
fyers_client - INFO - Fetching quotes for batch 62: 50 symbols
fyers_client - INFO - Fetching quotes for batch 63: 50 symbols
fyers_client - INFO - Fetching quotes for batch 64: 50 symbols
fyers_client - INFO - Fetching quotes for batch 65: 50 symbols
fyers_client - INFO - Fetching quotes for batch 66: 50 symbols
fyers_client - INFO - Fetching quotes for batch 67: 50 symbols
fyers_client - INFO - Fetching quotes for batch 68: 50 symbols
fyers_client - INFO - Fetching quotes for batch 69: 50 symbols
fyers_client - INFO - Fetching quotes for batch 70: 50 symbols
fyers_client - INFO - Fetching quotes for batch 71: 50 symbols
fyers_client - INFO - Fetching quotes for batch 72: 16 symbols
fyers_client - INFO - Successfully fetched market data for 3566 symbols
index_scanner - INFO - Received market data for 3566 symbols
index_scanner - INFO - Applying filters...
index_scanner - INFO - Applying filters - Volume Range: 10-*********, LTP Range: 2500-9000
index_scanner - INFO - CE/PE Pairing: Disabled
index_scanner - INFO - MAE Analysis: Enabled
index_scanner - INFO - MAE Length: 9, Source: close, Offset: 0, Smoothing: sma(9)
index_scanner - INFO - Timeframe - Interval: 60, Days: 30
index_scanner - INFO - First symbol for BANKNIFTY JUL: NSE:BANKNIFTY25JUL49000CE (LTP: 8211.0, Volume: 210)
index_scanner - INFO - First symbol for NIFTY JUL: NSE:NIFTY25JUL22500CE (LTP: 3020.0, Volume: 12150)
index_scanner - INFO - First symbol for BANKNIFTY AUG: NSE:BANKNIFTY25AUG52000CE (LTP: 5490.0, Volume: 315)
index_scanner - INFO - First symbol for NIFTY AUG: NSE:NIFTY25AUG23000CE (LTP: 2532.0, Volume: 225)
index_scanner - INFO - First symbol for BANKNIFTY SEP: NSE:BANKNIFTY25SEP55000CE (LTP: 2999.0, Volume: 455)
index_scanner - INFO - First symbol for NIFTY SEP: NSE:NIFTY25SEP17000CE (LTP: 8585.0, Volume: 450)
index_scanner - INFO - Initial Filtering Results:
index_scanner - INFO -   Total symbols processed: 3566
index_scanner - INFO -   Passed volume filter: 700
index_scanner - INFO -   Passed LTP filter: 42
index_scanner - INFO - CE/PE pairing filter is disabled
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL49000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 90 OHLC data points for NSE:BANKNIFTY25JUL49000CE
technical_indicators - INFO - High: 8211.0, Low: 8211.0, Latest MAE: 8077.0, Current price: 8211.0, Distance: 1.66%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL49500CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 89 OHLC data points for NSE:BANKNIFTY25JUL49500CE
technical_indicators - INFO - High: 7459.45, Low: 7459.45, Latest MAE: 7526.74, Current price: 7459.45, Distance: -0.89%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL50000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 139 OHLC data points for NSE:BANKNIFTY25JUL50000CE
technical_indicators - INFO - High: 7288.4, Low: 7226.0, Latest MAE: 7132.25, Current price: 7251.2, Distance: 1.67%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL51000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 139 OHLC data points for NSE:BANKNIFTY25JUL51000CE
technical_indicators - INFO - High: 6275.0, Low: 6251.35, Latest MAE: 6138.3, Current price: 6251.35, Distance: 1.84%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL51500CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 82 OHLC data points for NSE:BANKNIFTY25JUL51500CE
technical_indicators - INFO - High: 5808.55, Low: 5799.45, Latest MAE: 5697.07, Current price: 5799.45, Distance: 1.80%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL51700CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 7 OHLC data points for NSE:BANKNIFTY25JUL51700CE
index_scanner - WARNING - Insufficient historical data for MAE calculation: NSE:BANKNIFTY25JUL51700CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL52000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 147 OHLC data points for NSE:BANKNIFTY25JUL52000CE
technical_indicators - INFO - High: 5285.0, Low: 5270.0, Latest MAE: 5149.23, Current price: 5270.0, Distance: 2.35%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL52500CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 107 OHLC data points for NSE:BANKNIFTY25JUL52500CE
technical_indicators - INFO - High: 4824.25, Low: 4796.0, Latest MAE: 4656.06, Current price: 4824.25, Distance: 3.61%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL53000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 147 OHLC data points for NSE:BANKNIFTY25JUL53000CE
technical_indicators - INFO - High: 4311.55, Low: 4286.6, Latest MAE: 4195.37, Current price: 4300.25, Distance: 2.50%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL53500CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 133 OHLC data points for NSE:BANKNIFTY25JUL53500CE
technical_indicators - INFO - High: 3821.25, Low: 3821.25, Latest MAE: 3701.94, Current price: 3821.25, Distance: 3.22%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL53900CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 12 OHLC data points for NSE:BANKNIFTY25JUL53900CE
technical_indicators - INFO - High: 3245.0, Low: 3245.0, Latest MAE: 3171.07, Current price: 3245.0, Distance: 2.33%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 147 OHLC data points for NSE:BANKNIFTY25JUL54000CE
technical_indicators - INFO - High: 3360.0, Low: 3329.4, Latest MAE: 3233.58, Current price: 3360.0, Distance: 3.91%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54400CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 36 OHLC data points for NSE:BANKNIFTY25JUL54400CE
technical_indicators - INFO - High: 2965.8, Low: 2965.8, Latest MAE: 2892.94, Current price: 2965.8, Distance: 2.52%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54500CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 147 OHLC data points for NSE:BANKNIFTY25JUL54500CE
technical_indicators - INFO - High: 2880.0, Low: 2880.0, Latest MAE: 2780.81, Current price: 2880.0, Distance: 3.57%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL59900PE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 16 OHLC data points for NSE:BANKNIFTY25JUL59900PE
technical_indicators - INFO - High: 2799.85, Low: 2799.85, Latest MAE: 2946.33, Current price: 2799.85, Distance: -4.97%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL60000PE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 147 OHLC data points for NSE:BANKNIFTY25JUL60000PE
technical_indicators - INFO - High: 2760.0, Low: 2742.0, Latest MAE: 2865.45, Current price: 2742.0, Distance: -4.31%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL61000PE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 125 OHLC data points for NSE:BANKNIFTY25JUL61000PE
technical_indicators - INFO - High: 3705.55, Low: 3705.55, Latest MAE: 3819.01, Current price: 3705.55, Distance: -2.97%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL62000PE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 86 OHLC data points for NSE:BANKNIFTY25JUL62000PE
technical_indicators - INFO - High: 4695.85, Low: 4662.15, Latest MAE: 4809.75, Current price: 4662.15, Distance: -3.07%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL63000PE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 79 OHLC data points for NSE:BANKNIFTY25JUL63000PE
technical_indicators - INFO - High: 5700.0, Low: 5640.55, Latest MAE: 5755.77, Current price: 5640.55, Distance: -2.00%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL64000PE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 66 OHLC data points for NSE:BANKNIFTY25JUL64000PE
technical_indicators - INFO - High: 6662.8, Low: 6620.0, Latest MAE: 6735.59, Current price: 6630.0, Distance: -1.57%
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22500CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 140 OHLC data points for NSE:NIFTY25JUL22500CE
technical_indicators - INFO - High: 3024.75, Low: 3020.0, Latest MAE: 2994.45, Current price: 3020.0, Distance: 0.85%
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL23000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 146 OHLC data points for NSE:NIFTY25JUL23000CE
technical_indicators - INFO - High: 2538.9, Low: 2522.7, Latest MAE: 2501.33, Current price: 2538.9, Distance: 1.50%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG52000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 7 OHLC data points for NSE:BANKNIFTY25AUG52000CE
index_scanner - WARNING - Insufficient historical data for MAE calculation: NSE:BANKNIFTY25AUG52000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG53000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 63 OHLC data points for NSE:BANKNIFTY25AUG53000CE
technical_indicators - INFO - High: 4550.0, Low: 4500.0, Latest MAE: 4448.16, Current price: 4550.0, Distance: 2.29%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG54000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 105 OHLC data points for NSE:BANKNIFTY25AUG54000CE
technical_indicators - INFO - High: 3650.0, Low: 3611.65, Latest MAE: 3529.75, Current price: 3611.65, Distance: 2.32%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG54500CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 9 OHLC data points for NSE:BANKNIFTY25AUG54500CE
technical_indicators - INFO - High: 2909.95, Low: 2909.95, Latest MAE: 3103.3, Current price: 2909.95, Distance: -6.23%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG54900CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 7 OHLC data points for NSE:BANKNIFTY25AUG54900CE
index_scanner - WARNING - Insufficient historical data for MAE calculation: NSE:BANKNIFTY25AUG54900CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG55000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 145 OHLC data points for NSE:BANKNIFTY25AUG55000CE
technical_indicators - INFO - High: 2799.95, Low: 2755.0, Latest MAE: 2692.78, Current price: 2755.0, Distance: 2.31%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG60000PE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 53 OHLC data points for NSE:BANKNIFTY25AUG60000PE
technical_indicators - INFO - High: 2726.0, Low: 2726.0, Latest MAE: 2816.19, Current price: 2726.0, Distance: -3.20%
fyers_client - INFO - Fetching historical data for NSE:NIFTY25AUG23000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 116 OHLC data points for NSE:NIFTY25AUG23000CE
technical_indicators - INFO - High: 2532.0, Low: 2532.0, Latest MAE: 2571.05, Current price: 2532.0, Distance: -1.52%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP55000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 22 OHLC data points for NSE:BANKNIFTY25SEP55000CE
technical_indicators - INFO - High: 2999.0, Low: 2999.0, Latest MAE: 3049.65, Current price: 2999.0, Distance: -1.66%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP52500CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 85 OHLC data points for NSE:BANKNIFTY25SEP52500CE
technical_indicators - INFO - High: 5296.45, Low: 5296.45, Latest MAE: 5310.2, Current price: 5296.45, Distance: -0.26%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP54000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 121 OHLC data points for NSE:BANKNIFTY25SEP54000CE
technical_indicators - INFO - High: 4035.0, Low: 4035.0, Latest MAE: 3947.02, Current price: 4035.0, Distance: 2.23%
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP55500CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 142 OHLC data points for NSE:BANKNIFTY25SEP55500CE
technical_indicators - INFO - High: 2750.0, Low: 2750.0, Latest MAE: 2719.63, Current price: 2750.0, Distance: 1.12%
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP17000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 57 OHLC data points for NSE:NIFTY25SEP17000CE
technical_indicators - INFO - High: 8585.0, Low: 8500.0, Latest MAE: 8549.97, Current price: 8585.0, Distance: 0.41%
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP18000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 61 OHLC data points for NSE:NIFTY25SEP18000CE
technical_indicators - INFO - High: 7650.0, Low: 7650.0, Latest MAE: 7680.26, Current price: 7650.0, Distance: -0.39%
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP20000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 104 OHLC data points for NSE:NIFTY25SEP20000CE
technical_indicators - INFO - High: 5702.7, Low: 5702.7, Latest MAE: 5712.88, Current price: 5702.7, Distance: -0.18%
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP21000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 120 OHLC data points for NSE:NIFTY25SEP21000CE
technical_indicators - INFO - High: 4719.0, Low: 4704.3, Latest MAE: 4731.83, Current price: 4719.0, Distance: -0.27%
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP22000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 125 OHLC data points for NSE:NIFTY25SEP22000CE
technical_indicators - INFO - High: 3755.6, Low: 3750.65, Latest MAE: 3721.21, Current price: 3755.0, Distance: 0.91%
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP23000CE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 142 OHLC data points for NSE:NIFTY25SEP23000CE
technical_indicators - INFO - High: 2812.85, Low: 2790.0, Latest MAE: 2771.61, Current price: 2812.85, Distance: 1.49%
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP29000PE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 91 OHLC data points for NSE:NIFTY25SEP29000PE
technical_indicators - INFO - High: 3144.15, Low: 3125.95, Latest MAE: 3149.34, Current price: 3125.95, Distance: -0.74%
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP30000PE - Interval: 60, Days: 30
fyers_client - INFO - Successfully fetched 26 OHLC data points for NSE:NIFTY25SEP30000PE
technical_indicators - INFO - High: 4096.75, Low: 4093.55, Latest MAE: 4077.1, Current price: 4093.55, Distance: 0.40%
index_scanner - INFO - Final Filtering Results:
index_scanner - INFO -   Symbols after CE/PE pairing: 42
index_scanner - INFO -   Passed MAE filter: 1
index_scanner - INFO -   Final symbols after all filters: 1
index_scanner - INFO -   Overall success rate: 0.03%
index_scanner - INFO - Scanning complete. Found 1 symbols matching criteria
fyers_config - INFO - Generating summary statistics...
fyers_config - INFO - Generating reports...
report_generator - INFO - Output directory ready: reports
report_generator - INFO - Sorted 1 symbols by underlying and month sequence
report_generator - INFO - CSV report created successfully: reports\index_scan_20250706_000334.csv
report_generator - INFO - Report contains 1 symbols
report_generator - INFO - Summary report created successfully: reports\index_scan_summary_20250706_000334.txt
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER COMPLETED SUCCESSFULLY
fyers_config - INFO - ================================================================================
fyers_config - INFO - End time: 2025-07-06 00:03:35
fyers_config - INFO - CSV Report: reports\index_scan_20250706_000334.csv
fyers_config - INFO - Summary Report: reports\index_scan_summary_20250706_000334.txt
fyers_config - INFO - Total symbols found: 1
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-06 00:05:35
fyers_config - INFO - Loading configuration...
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\index_scanner\config.yaml
config_loader - INFO - Configuration validation successful
fyers_config - INFO - Configuration loaded and validated successfully
fyers_config - INFO - Target symbols: ['NIFTY', 'BANKNIFTY']
fyers_config - INFO - Volume filter: >= 10
fyers_config - INFO - LTP filter: 2500 - 9000
fyers_config - INFO - Initializing Index Scanner...
symbol_parser - INFO - Using configured target symbols: BANKNIFTY, NIFTY
fyers_config - INFO - Starting symbol scanning process...
index_scanner - INFO - Starting index scanner...
index_scanner - INFO - Authenticating with Fyers API...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
index_scanner - INFO - Loading symbols from CSV...
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Loaded 3566 symbols from CSV for target months
symbol_parser - INFO - Filtered to 3566 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Prepared 3566 symbols for scanning
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Loaded 3566 symbols from CSV for target months
symbol_parser - INFO - Filtered to 3566 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Saved 3566 filtered symbols to reports\filtered_symbols.csv
index_scanner - INFO - Found 3566 symbols to scan
index_scanner - INFO - Fetching market data from Fyers API...
fyers_client - INFO - Fetching quotes for batch 1: 50 symbols
fyers_client - INFO - Fetching quotes for batch 2: 50 symbols
fyers_client - INFO - Fetching quotes for batch 3: 50 symbols
fyers_client - INFO - Fetching quotes for batch 4: 50 symbols
fyers_client - INFO - Fetching quotes for batch 5: 50 symbols
fyers_client - INFO - Fetching quotes for batch 6: 50 symbols
fyers_client - INFO - Fetching quotes for batch 7: 50 symbols
fyers_client - INFO - Fetching quotes for batch 8: 50 symbols
fyers_client - INFO - Fetching quotes for batch 9: 50 symbols
fyers_client - INFO - Fetching quotes for batch 10: 50 symbols
fyers_client - INFO - Fetching quotes for batch 11: 50 symbols
fyers_client - INFO - Fetching quotes for batch 12: 50 symbols
fyers_client - INFO - Fetching quotes for batch 13: 50 symbols
fyers_client - INFO - Fetching quotes for batch 14: 50 symbols
fyers_client - INFO - Fetching quotes for batch 15: 50 symbols
fyers_client - INFO - Fetching quotes for batch 16: 50 symbols
fyers_client - INFO - Fetching quotes for batch 17: 50 symbols
fyers_client - INFO - Fetching quotes for batch 18: 50 symbols
fyers_client - INFO - Fetching quotes for batch 19: 50 symbols
fyers_client - INFO - Fetching quotes for batch 20: 50 symbols
fyers_client - INFO - Fetching quotes for batch 21: 50 symbols
fyers_client - INFO - Fetching quotes for batch 22: 50 symbols
fyers_client - INFO - Fetching quotes for batch 23: 50 symbols
fyers_client - INFO - Fetching quotes for batch 24: 50 symbols
fyers_client - INFO - Fetching quotes for batch 25: 50 symbols
fyers_client - INFO - Fetching quotes for batch 26: 50 symbols
fyers_client - INFO - Fetching quotes for batch 27: 50 symbols
fyers_client - INFO - Fetching quotes for batch 28: 50 symbols
fyers_client - INFO - Fetching quotes for batch 29: 50 symbols
fyers_client - INFO - Fetching quotes for batch 30: 50 symbols
fyers_client - INFO - Fetching quotes for batch 31: 50 symbols
fyers_client - INFO - Fetching quotes for batch 32: 50 symbols
fyers_client - INFO - Fetching quotes for batch 33: 50 symbols
fyers_client - INFO - Fetching quotes for batch 34: 50 symbols
fyers_client - INFO - Fetching quotes for batch 35: 50 symbols
fyers_client - INFO - Fetching quotes for batch 36: 50 symbols
fyers_client - INFO - Fetching quotes for batch 37: 50 symbols
fyers_client - INFO - Fetching quotes for batch 38: 50 symbols
fyers_client - INFO - Fetching quotes for batch 39: 50 symbols
fyers_client - INFO - Fetching quotes for batch 40: 50 symbols
fyers_client - INFO - Fetching quotes for batch 41: 50 symbols
fyers_client - INFO - Fetching quotes for batch 42: 50 symbols
fyers_client - INFO - Fetching quotes for batch 43: 50 symbols
fyers_client - INFO - Fetching quotes for batch 44: 50 symbols
fyers_client - INFO - Fetching quotes for batch 45: 50 symbols
fyers_client - INFO - Fetching quotes for batch 46: 50 symbols
fyers_client - INFO - Fetching quotes for batch 47: 50 symbols
fyers_client - INFO - Fetching quotes for batch 48: 50 symbols
fyers_client - INFO - Fetching quotes for batch 49: 50 symbols
fyers_client - INFO - Fetching quotes for batch 50: 50 symbols
fyers_client - INFO - Fetching quotes for batch 51: 50 symbols
fyers_client - INFO - Fetching quotes for batch 52: 50 symbols
fyers_client - INFO - Fetching quotes for batch 53: 50 symbols
fyers_client - INFO - Fetching quotes for batch 54: 50 symbols
fyers_client - INFO - Fetching quotes for batch 55: 50 symbols
fyers_client - INFO - Fetching quotes for batch 56: 50 symbols
fyers_client - INFO - Fetching quotes for batch 57: 50 symbols
fyers_client - INFO - Fetching quotes for batch 58: 50 symbols
fyers_client - INFO - Fetching quotes for batch 59: 50 symbols
fyers_client - INFO - Fetching quotes for batch 60: 50 symbols
fyers_client - INFO - Fetching quotes for batch 61: 50 symbols
fyers_client - INFO - Fetching quotes for batch 62: 50 symbols
fyers_client - INFO - Fetching quotes for batch 63: 50 symbols
fyers_client - INFO - Fetching quotes for batch 64: 50 symbols
fyers_client - INFO - Fetching quotes for batch 65: 50 symbols
fyers_client - INFO - Fetching quotes for batch 66: 50 symbols
